import { v4 as uuidv4 } from "uuid";
import { appointmentMetadata, getSnomedCtCode, appointmentDiv } from "../../../utils/fhir.constants.js";
import { generateSnomedCtCode } from "../common_resources/snomed_ct_code.generator.fhir.js";

export const generateAppointmentResource = async (appointment, patientResource, practitionerResources, serviceRequestResources, conditionResources, doctors) => {
    const id = uuidv4();

    const getSnomedDataAppointmentServiceCategories = await Promise.all(
        appointment.serviceCategories.map(category => generateSnomedCtCode(category))
    );

    const getSnomedDataAppointmentServiceTypes = await Promise.all(
        appointment.serviceTypes.map(type => generateSnomedCtCode(type))
    );

    const getSnomedDataAppointmentType = await generateSnomedCtCode(appointment.appointmentType);

    const normalize = (str) => str.trim().toLowerCase();
    const normalizedDoctors = doctors.map(normalize);

    const matchingPractitioners = practitionerResources
        .filter(practitioner => practitioner.resource.name.some(nameObj => normalizedDoctors.includes(normalize(nameObj.text))))
        .map(practitioner => ({
            actor: {
                reference: `urn:uuid:${practitioner.resource.id}`,
                display: practitioner.resource.resourceType
            },
            status: "accepted"
        }));
        const getSnomedDataAppointmentSpecialty = await Promise.all(
            appointment.specialty.map(type => generateSnomedCtCode(type))
        );
    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'Appointment',
            id,
            meta: appointmentMetadata(),
            status: appointment.status,
            description: appointment.description,
            start: appointment.start,
            end: appointment.end,
            created: appointment.created,
            serviceCategory: getSnomedDataAppointmentServiceCategories.map(data =>
                getSnomedCtCode(data.conceptId, data.term)
            ),
            serviceType: getSnomedDataAppointmentServiceTypes.map(data =>
                getSnomedCtCode(data.conceptId, data.term)
            ),
            appointmentType: getSnomedCtCode(getSnomedDataAppointmentType.conceptId, getSnomedDataAppointmentType.term),
            // Only include reasonReference if there are valid references after filtering
            ...(() => {
                if (appointment.reasonReference && appointment.reasonReference.length > 0) {
                    const validReasonReferences = appointment.reasonReference
                        .map(reason => conditionResources.find(condition => condition.resource.code.text.trim().toLowerCase() === reason.trim().toLowerCase()))
                        .filter(Boolean)
                        .map(condition => ({
                            reference: `urn:uuid:${condition.resource.id}`,
                            display: condition.resource.resourceType
                        }));
                    return validReasonReferences.length > 0 ? { reasonReference: validReasonReferences } : {};
                }
                return {};
            })(),
            // Only include basedOn if there are valid references after filtering
            ...(() => {
                if (appointment.basedOnServices && appointment.basedOnServices.length > 0) {
                    const validBasedOnServices = appointment.basedOnServices
                        .map(service =>
                            serviceRequestResources.find(serviceRequestResource =>
                                serviceRequestResource.resource.category.some(category =>
                                    normalize(category.text) === normalize(service)
                                )
                            )
                        )
                        .filter(Boolean)
                        .map(serviceRequestResource => ({
                            reference: `urn:uuid:${serviceRequestResource.resource.id}`,
                            display: serviceRequestResource.resource.resourceType
                        }));
                    return validBasedOnServices.length > 0 ? { basedOn: validBasedOnServices } : {};
                }
                return {};
            })(),
                specialty:getSnomedDataAppointmentSpecialty.map(data =>
                getSnomedCtCode(data.conceptId, data.term)
            ),
            participant: [
                {
                    actor: {
                        reference: `urn:uuid:${patientResource.resource.id}`,
                        display: patientResource.resource.resourceType
                    },
                    status: "accepted"
                },
                ...matchingPractitioners
            ],
            text: appointmentDiv()
        }
    };
};
