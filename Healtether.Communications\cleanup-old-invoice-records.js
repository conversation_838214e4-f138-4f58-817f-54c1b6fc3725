import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { InvoiceRecordSchema } from './schema/fhir_schema/invoice.schema.fhir.js';

// Load environment variables
dotenv.config();

// Configuration
const CONFIG = {
  // Default: Delete records older than 90 days
  DEFAULT_DAYS_OLD: 0,
  // Batch size for processing
  BATCH_SIZE: 100,
  // Dry run mode (set to false to actually delete)
  DRY_RUN: false
};

class InvoiceRecordCleanup {
  constructor() {
    this.db = null;
    this.models = {};
    this.modelNames = ['InvoiceReportFHIRRecord','WellnessReportFHIRRecord','DiagnosticReportFHIRRecord','DischargeSummaryFHIRRecord','OPConsultFHIRRecord','PrescriptionFHIRRecord','HealthDocumentFHIRRecord','ImmunizationReportFHIRRecord'];
  }

  async connect() {
    try {
      // Get MongoDB URI from environment
      const portalDbConnectionString = process.env.MONGODB_PORTAL_URI;
      if (!portalDbConnectionString) {
        throw new Error('❌ MONGODB_PORTAL_URI is not defined in environment variables');
      }

      console.log('🔌 Connecting to database...');
      this.db = mongoose.createConnection(portalDbConnectionString);

      // Register all FHIR record models
      for (const modelName of this.modelNames) {
        this.models[modelName] = this.db.model(modelName, InvoiceRecordSchema);
      }

      console.log('✅ Database connected successfully');
      console.log(`📋 Registered ${this.modelNames.length} FHIR record models: ${this.modelNames.join(', ')}`);
      return true;
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
  }

  async disconnect() {
    if (this.db && this.db.readyState === 1) {
      await this.db.close();
      console.log('🔌 Database connection closed');
    }
  }

  calculateCutoffDate(daysOld = CONFIG.DEFAULT_DAYS_OLD) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    return cutoffDate;
  }

  async countOldRecords(daysOld = CONFIG.DEFAULT_DAYS_OLD) {
    try {
      const cutoffDate = this.calculateCutoffDate(daysOld);

      // Count records where created date is older than cutoff
      // Using $or to handle different possible date field structures
      const query = {
        $or: [
          { 'general.created': { $lt: cutoffDate } },
          { createdAt: { $lt: cutoffDate } },
          { _id: { $lt: mongoose.Types.ObjectId.createFromTime(cutoffDate.getTime() / 1000) } }
        ]
      };

      let totalCount = 0;
      const modelCounts = {};

      // Count records for each model
      for (const modelName of this.modelNames) {
        const count = await this.models[modelName].countDocuments(query);
        modelCounts[modelName] = count;
        totalCount += count;
      }

      console.log(`📊 Found ${totalCount} FHIR records older than ${daysOld} days (before ${cutoffDate.toISOString()})`);
      for (const [modelName, count] of Object.entries(modelCounts)) {
        if (count > 0) {
          console.log(`   ${modelName}: ${count} records`);
        }
      }

      return { count: totalCount, cutoffDate, query, modelCounts };
    } catch (error) {
      console.error('❌ Error counting old records:', error.message);
      throw error;
    }
  }

  async findOldRecords(daysOld = CONFIG.DEFAULT_DAYS_OLD, limit = 10) {
    try {
      const { query, modelCounts } = await this.countOldRecords(daysOld);

      let allRecords = [];
      let recordCount = 0;

      // Collect records from all models
      for (const modelName of this.modelNames) {
        if (modelCounts[modelName] > 0 && recordCount < limit) {
          const remainingLimit = limit - recordCount;
          const records = await this.models[modelName]
            .find(query)
            .limit(remainingLimit)
            .select('fhirId general.created patient.name patient.id createdAt _id')
            .lean();

          // Add model name to each record for identification
          const recordsWithModel = records.map(record => ({ ...record, modelName }));
          allRecords = allRecords.concat(recordsWithModel);
          recordCount += records.length;
        }
      }

      console.log(`📋 Sample of old records (showing first ${Math.min(limit, allRecords.length)}):`);
      allRecords.forEach((record, index) => {
        const createdDate = record.general?.created || record.createdAt || 'Unknown';
        const patientName = record.patient?.name || 'Unknown Patient';
        const patientId = record.patient?.id || 'Unknown ID';

        console.log(`  ${index + 1}. Model: ${record.modelName}`);
        console.log(`     FHIR ID: ${record.fhirId}`);
        console.log(`     Patient: ${patientName} (ID: ${patientId})`);
        console.log(`     Created: ${createdDate}`);
        console.log(`     MongoDB ID: ${record._id}`);
        console.log('');
      });

      return allRecords;
    } catch (error) {
      console.error('❌ Error finding old records:', error.message);
      throw error;
    }
  }

  async deleteOldRecords(daysOld = CONFIG.DEFAULT_DAYS_OLD, dryRun = CONFIG.DRY_RUN) {
    try {
      const { count, cutoffDate, query, modelCounts } = await this.countOldRecords(daysOld);

      if (count === 0) {
        console.log('✅ No old records found to delete');
        return { deleted: 0, errors: [] };
      }

      if (dryRun) {
        console.log(`🔍 DRY RUN MODE: Would delete ${count} FHIR records older than ${daysOld} days`);
        for (const [modelName, modelCount] of Object.entries(modelCounts)) {
          if (modelCount > 0) {
            console.log(`   ${modelName}: ${modelCount} records`);
          }
        }
        console.log('   Set dryRun=false to actually perform deletion');
        return { deleted: 0, errors: [], dryRun: true };
      }

      console.log(`⚠️  DANGER: About to delete ${count} FHIR records older than ${daysOld} days`);
      console.log(`   Cutoff date: ${cutoffDate.toISOString()}`);

      // In a real scenario, you might want to add a confirmation prompt here
      // For now, we'll proceed with the deletion

      let totalDeleted = 0;
      let errors = [];
      const deletedByModel = {};

      // Process each model separately
      for (const modelName of this.modelNames) {
        if (modelCounts[modelName] === 0) continue;

        console.log(`\n🗑️  Processing ${modelName} (${modelCounts[modelName]} records)...`);
        let modelDeleted = 0;
        let processed = 0;
        const modelCount = modelCounts[modelName];

        // Process in batches to avoid memory issues
        while (processed < modelCount) {
          try {
            const batch = await this.models[modelName]
              .find(query)
              .limit(CONFIG.BATCH_SIZE)
              .select('_id fhirId');

            if (batch.length === 0) break;

            const batchIds = batch.map(record => record._id);
            const deleteResult = await this.models[modelName].deleteMany({
              _id: { $in: batchIds }
            });

            modelDeleted += deleteResult.deletedCount;
            totalDeleted += deleteResult.deletedCount;
            processed += batch.length;

            console.log(`   Deleted batch: ${deleteResult.deletedCount}/${batch.length} records (Model total: ${modelDeleted}/${modelCount})`);

            // Small delay to prevent overwhelming the database
            await new Promise(resolve => setTimeout(resolve, 100));

          } catch (batchError) {
            console.error(`❌ Error deleting batch for ${modelName} starting at ${processed}:`, batchError.message);
            errors.push({
              model: modelName,
              batch: Math.floor(processed / CONFIG.BATCH_SIZE) + 1,
              error: batchError.message
            });
            processed += CONFIG.BATCH_SIZE; // Skip this batch
          }
        }

        deletedByModel[modelName] = modelDeleted;
        console.log(`✅ ${modelName}: ${modelDeleted} records deleted`);
      }

      console.log(`\n✅ Deletion completed: ${totalDeleted} total records deleted`);
      for (const [modelName, deleted] of Object.entries(deletedByModel)) {
        if (deleted > 0) {
          console.log(`   ${modelName}: ${deleted} records`);
        }
      }

      if (errors.length > 0) {
        console.log(`⚠️  Errors encountered: ${errors.length} batches failed`);
        errors.forEach(error => {
          console.log(`   ${error.model} Batch ${error.batch}: ${error.error}`);
        });
      }

      return { deleted: totalDeleted, errors, deletedByModel };
    } catch (error) {
      console.error('❌ Error during deletion process:', error.message);
      throw error;
    }
  }

  async getRecordStats() {
    try {
      // Get total counts for all models
      let totalRecords = 0;
      const totalByModel = {};

      for (const modelName of this.modelNames) {
        const count = await this.models[modelName].countDocuments();
        totalByModel[modelName] = count;
        totalRecords += count;
      }

      const lastDays = await this.countOldRecords();
      const last30Days = await this.countOldRecords(30);
      const last90Days = await this.countOldRecords(90);
      const last180Days = await this.countOldRecords(180);
      const last365Days = await this.countOldRecords(365);

      console.log('📈 FHIR Record Statistics:');
      console.log(`   Total records across all models: ${totalRecords}`);

      // Show breakdown by model
      for (const [modelName, count] of Object.entries(totalByModel)) {
        if (count > 0) {
          console.log(`     ${modelName}: ${count} records`);
        }
      }

      console.log(`   Older than ${CONFIG.DEFAULT_DAYS_OLD} days: ${lastDays.count}`);
      console.log(`   Older than 30 days: ${last30Days.count}`);
      console.log(`   Older than 90 days: ${last90Days.count}`);
      console.log(`   Older than 180 days: ${last180Days.count}`);
      console.log(`   Older than 365 days: ${last365Days.count}`);

      return {
        total: totalRecords,
        totalByModel,
        lastDays: lastDays.count,
        last30Days: last30Days.count,
        last90Days: last90Days.count,
        last180Days: last180Days.count,
        last365Days: last365Days.count
      };
    } catch (error) {
      console.error('❌ Error getting record statistics:', error.message);
      throw error;
    }
  }
}

// Main execution function
async function main() {
  const cleanup = new InvoiceRecordCleanup();
  
  try {
    // Connect to database
    const connected = await cleanup.connect();
    if (!connected) {
      process.exit(1);
    }

    console.log('🧹 FHIR Record Cleanup Tool');
    console.log('============================\n');

    // Get overall statistics and log them
    await cleanup.getRecordStats();
    console.log('');

    // Show sample of old records using CONFIG.DEFAULT_DAYS_OLD
    console.log(`📋 Sample of records to be cleaned (${CONFIG.DEFAULT_DAYS_OLD}+ days old):`);
    await cleanup.findOldRecords(CONFIG.DEFAULT_DAYS_OLD, 5);

    // Perform cleanup using configured values
    console.log('🗑️  Starting cleanup process...');
    const result = await cleanup.deleteOldRecords(CONFIG.DEFAULT_DAYS_OLD, CONFIG.DRY_RUN);
    
    if (result.dryRun) {
      console.log('\n💡 To actually delete records, set CONFIG.DRY_RUN = false in the script');
    }

  } catch (error) {
    console.error('💥 Script execution failed:', error.message);
    process.exit(1);
  } finally {
    await cleanup.disconnect();
  }
}

// Export for use as module or run directly
export { InvoiceRecordCleanup };

// Run if this file is executed directly
// Debug: Check if we should run main
console.log('Debug: import.meta.url =', import.meta.url);
console.log('Debug: process.argv[1] =', process.argv[1]);
console.log('Debug: file:// + process.argv[1] =', `file://${process.argv[1]}`);

// Alternative check for direct execution
const isMainModule = import.meta.url === `file://${process.argv[1]}` ||
                    import.meta.url.endsWith(process.argv[1]) ||
                    process.argv[1].endsWith('cleanup-old-invoice-records.js');

console.log('Debug: isMainModule =', isMainModule);

if (isMainModule) {
  console.log('🚀 Running script directly...');
  main();
} else {
  console.log('📦 Script imported as module');
}
