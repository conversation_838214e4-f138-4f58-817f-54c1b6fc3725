import { v4 as uuidv4 } from "uuid";
import { AbdmAccessToken } from "./abdm-access-token.js";

// Default retry configuration for ABHA API calls
export const DEFAULT_RETRY_CONFIG = {
    maxRetries: 3,
    baseDelay: 1000,
    timeout: 30000
};

// Utility function to create retry options with custom overrides
export const createRetryOptions = (overrides = {}) => ({
    ...DEFAULT_RETRY_CONFIG,
    ...overrides
});

export const fetchAbhaApi = async (url, body, method, txnId, tToken, xToken, options = {}) => {
    const {
        maxRetries = 3,
        baseDelay = 1000,
        timeout = 30000,
        retryCount = 0
    } = options;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
        const token = await AbdmAccessToken();
        const isoTimestamp = new Date().toISOString();
        const randomUUID = uuidv4();

        console.log(`fetchAbhaApi ABDM attempt ${retryCount + 1}/${maxRetries + 1} - randomUUID:`, randomUUID);

        const headers = {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            TIMESTAMP: isoTimestamp,
            "REQUEST-ID": randomUUID,
            Transaction_Id: txnId || "",
            "T-token": tToken || "",
            "X-token": xToken || "",
        };

        // Define request options
        const requestOptions = {
            method: method || "POST",
            headers,
            signal: controller.signal,
        };

        // Only include body for non-GET/HEAD methods
        if (body && method !== "GET" && method !== "HEAD") {
            requestOptions.body = JSON.stringify(body);
        }

        const result = await fetch(`${process.env.ABHA_BASE_URL}${url}`, requestOptions);

        clearTimeout(timeoutId);

        console.log(`${process.env.ABHA_BASE_URL}${url}`, result.ok, result.status);

        // Handle HTTP errors that should be retried (5xx errors)
        if (!result.ok && result.status >= 500 && retryCount < maxRetries) {
            const delay = baseDelay * Math.pow(2, retryCount);
            console.log(`HTTP ${result.status} error detected. Retrying in ${delay}ms... (attempt ${retryCount + 1}/${maxRetries})`);

            await new Promise(resolve => setTimeout(resolve, delay));
            return fetchAbhaApi(url, body, method, txnId, tToken, xToken, {
                ...options,
                retryCount: retryCount + 1
            });
        }

        return result;
    } catch (error) {
        clearTimeout(timeoutId);
        console.error(`Error in fetchAbhaApi ABDM (attempt ${retryCount + 1}):`, error);

        // Create detailed error information
        const errorDetails = {
            message: error.message,
            code: error.code || "UNKNOWN",
            name: error.name,
            url: `${process.env.ABHA_BASE_URL}${url}`,
            attempt: retryCount + 1,
            maxRetries: maxRetries + 1
        };

        // Determine if we should retry based on error type
        const shouldRetry = retryCount < maxRetries && (
            error.code === "ECONNRESET" ||
            error.code === "ECONNREFUSED" ||
            error.code === "ETIMEDOUT" ||
            error.code === "ENOTFOUND" ||
            error.name === "AbortError" ||
            error.message.includes("timeout") ||
            error.message.includes("refused") ||
            error.message.includes("reset")
        );

        if (shouldRetry) {
            const delay = baseDelay * Math.pow(2, retryCount);
            console.log(`${error.code || error.name} detected. Retrying in ${delay}ms... (attempt ${retryCount + 1}/${maxRetries})`);

            await new Promise(resolve => setTimeout(resolve, delay));
            return fetchAbhaApi(url, body, method, txnId, tToken, xToken, {
                ...options,
                retryCount: retryCount + 1
            });
        }

        // If we've exhausted retries or it's a non-retryable error, throw with details
        const finalError = new Error(`fetchAbhaApi ABDM failed after ${retryCount + 1} attempts: ${error.message}`);
        finalError.details = errorDetails;
        finalError.originalError = error;
        throw finalError;
    }
};


export const fetchAbdmSession = async (data, options = {}) => {
    const {
        maxRetries = 3,
        baseDelay = 1000,
        timeout = 30000,
        retryCount = 0
    } = { ...DEFAULT_RETRY_CONFIG, ...options };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
        console.log(`fetchAbdmSession attempt ${retryCount + 1}/${maxRetries + 1}`);

        const result = await fetch(process.env.AUTH_TOKEN_URL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
            signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // Handle HTTP errors that should be retried (5xx errors)
        if (!result.ok && result.status >= 500 && retryCount < maxRetries) {
            const delay = baseDelay * Math.pow(2, retryCount);
            console.log(`HTTP ${result.status} error in fetchAbdmSession. Retrying in ${delay}ms... (attempt ${retryCount + 1}/${maxRetries})`);

            await new Promise(resolve => setTimeout(resolve, delay));
            return fetchAbdmSession(data, {
                ...options,
                retryCount: retryCount + 1
            });
        }

        if (!result.ok) {
            throw new Error(`Failed to fetch token. Status: ${result.status}`);
        }

        const response = await result.json();
        console.log("fetchAbdmSession response", response);
        return response;
    } catch (error) {
        clearTimeout(timeoutId);
        console.error(`Error in fetchAbdmSession (attempt ${retryCount + 1}):`, error);

        // Determine if we should retry based on error type
        const shouldRetry = retryCount < maxRetries && (
            error.code === "ECONNRESET" ||
            error.code === "ECONNREFUSED" ||
            error.code === "ETIMEDOUT" ||
            error.code === "ENOTFOUND" ||
            error.name === "AbortError" ||
            error.message.includes("timeout") ||
            error.message.includes("refused") ||
            error.message.includes("reset")
        );

        if (shouldRetry) {
            const delay = baseDelay * Math.pow(2, retryCount);
            console.log(`${error.code || error.name} detected in fetchAbdmSession. Retrying in ${delay}ms... (attempt ${retryCount + 1}/${maxRetries})`);

            await new Promise(resolve => setTimeout(resolve, delay));
            return fetchAbdmSession(data, {
                ...options,
                retryCount: retryCount + 1
            });
        }

        // If we've exhausted retries or it's a non-retryable error, throw with details
        const finalError = new Error(`fetchAbdmSession failed after ${retryCount + 1} attempts: ${error.message}`);
        finalError.details = {
            message: error.message,
            code: error.code || "UNKNOWN",
            name: error.name,
            url: process.env.AUTH_TOKEN_URL,
            attempt: retryCount + 1,
            maxRetries: maxRetries + 1
        };
        finalError.originalError = error;
        throw finalError;
    }
}

export const ackProfileShare = async (path, req, payload) => {
    const body = req.body;
    const headers = { ...req.headers };
    const token = await AbdmAccessToken();
    const isoTimestamp = new Date().toISOString();
    const randomUUID = uuidv4();
    const cmId = process.env.CM_ID;

    if (!body) {
        return {
            isSuccess: false,
            status: 400,
            data: { error: { message: "Invalid request body" } },
        };
    }

    const postHeaders = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        TIMESTAMP: isoTimestamp,
        "REQUEST-ID": randomUUID,
        "X-CM-ID": cmId
    };

    console.log("postHeaders",postHeaders)
    const requestOptions = {
        method: "POST",
        body: JSON.stringify(payload),
        headers:postHeaders,
    };

    const result = await fetch(`${process.env.ABHA_M2_BASE_URL}${path}`, requestOptions);
    return result;
};
